"""
Configuration manager for QMigrator AI FastAPI application.

This module provides centralized configuration management using environment variables only,
without dependency on .env files for production deployment flexibility.
"""

import os
from pydantic import BaseModel, Field
from config.model_config import (
    ModelConfig,
    default_model_config,
    AzureOpenAIConfig,
    OpenAIConfig,
    AnthropicConfig,
    GroqConfig,
    OllamaConfig,
    GeminiConfig
)


class ConfigManager(BaseModel):
    """
    Configuration manager for QMigrator AI FastAPI application.
    
    Manages all configuration through environment variables without .env file dependency.
    Provides type-safe configuration with Pydantic validation.
    """
    
    # Database configuration (required from environment variables)
    source_database: str = Field(
        default="",
        description="Source database name for migration (required via SOURCE_DATABASE env var)"
    )
    
    target_database: str = Field(
        default="",
        description="Target database name for migration (required via TARGET_DATABASE env var)"
    )
    
    # Primary LLM provider selection
    provider: str = Field(
        default="azure_openai",
        description="Primary LLM provider (azure_openai, openai, anthropic, groq, gemini, ollama)"
    )
    
    # Model configuration for all providers
    models_config: ModelConfig = Field(
        default_factory=lambda: default_model_config,
        description="Model configuration for all supported LLM providers"
    )
    
    # Workflow settings
    output_path: str = Field(
        default="output_files",
        description="Path to save workflow output files"
    )
    
    max_iterations: int = Field(
        default=3,
        description="Maximum number of workflow iterations"
    )
    
    def __init__(self, **data):
        super().__init__(**data)
        self.load_from_env()
    
    def load_from_env(self):
        """Load configuration from environment variables."""
        # Load provider from environment
        provider = os.getenv('LLM_PROVIDER')
        if provider:
            self.provider = provider
        
        # Load database names from environment (required)
        source_db = os.getenv('SOURCE_DATABASE')
        if not source_db:
            raise ValueError("SOURCE_DATABASE environment variable is required")
        self.source_database = source_db
        
        target_db = os.getenv('TARGET_DATABASE')
        if not target_db:
            raise ValueError("TARGET_DATABASE environment variable is required")
        self.target_database = target_db
        
        # Load output path from environment
        output_path = os.getenv('OUTPUT_PATH')
        if output_path:
            self.output_path = output_path
        
        # Load max iterations from environment
        max_iterations = os.getenv('MAX_ITERATIONS')
        if max_iterations:
            try:
                self.max_iterations = int(max_iterations)
            except ValueError:
                print(f"Warning: Invalid MAX_ITERATIONS value '{max_iterations}', using default")
        
        # Load provider-specific configurations
        if self.provider == 'azure_openai':
            self.models_config.azure_openai = AzureOpenAIConfig()
        elif self.provider == 'openai':
            self.models_config.openai = OpenAIConfig()
        elif self.provider == 'anthropic':
            self.models_config.anthropic = AnthropicConfig()
        elif self.provider == 'groq':
            self.models_config.groq = GroqConfig()
        elif self.provider == 'gemini':
            self.models_config.gemini = GeminiConfig()
        elif self.provider == 'ollama':
            self.models_config.ollama = OllamaConfig()
    
    def get_llm_provider(self) -> str:
        """Get the configured LLM provider."""
        return self.provider
    
    def get_source_database(self) -> str:
        """Get the source database name."""
        return self.source_database
    
    def get_target_database(self) -> str:
        """Get the target database name."""
        return self.target_database
    
    def get_migration_direction(self) -> str:
        """Get the migration direction string."""
        return f"{self.source_database} to {self.target_database}"
    
    def get_output_path(self) -> str:
        """Get the output path for workflow files."""
        return self.output_path
    
    def get_max_iterations(self) -> int:
        """Get the maximum number of workflow iterations."""
        return self.max_iterations
    
    def get_provider_config(self):
        """Get the configuration for the current provider."""
        if self.provider == 'azure_openai':
            return self.models_config.azure_openai
        elif self.provider == 'openai':
            return self.models_config.openai
        elif self.provider == 'anthropic':
            return self.models_config.anthropic
        elif self.provider == 'groq':
            return self.models_config.groq
        elif self.provider == 'gemini':
            return self.models_config.gemini
        elif self.provider == 'ollama':
            return self.models_config.ollama
        else:
            raise ValueError(f"Unsupported provider: {self.provider}")
    
    def validate_configuration(self) -> bool:
        """
        Validate that all required configuration is present.
        
        Returns:
            bool: True if configuration is valid, raises ValueError otherwise
        """
        if not self.source_database:
            raise ValueError("SOURCE_DATABASE is required")
        
        if not self.target_database:
            raise ValueError("TARGET_DATABASE is required")
        
        # Validate provider-specific configuration
        provider_config = self.get_provider_config()
        
        if self.provider == 'azure_openai':
            if not provider_config.api_key:
                raise ValueError("AZURE_OPENAI_API_KEY is required")
            if not provider_config.endpoint:
                raise ValueError("AZURE_OPENAI_ENDPOINT is required")
            if not provider_config.deployment_name:
                raise ValueError("AZURE_OPENAI_DEPLOYMENT_NAME is required")
        
        elif self.provider == 'openai':
            if not provider_config.api_key:
                raise ValueError("OPENAI_API_KEY is required")
        
        elif self.provider == 'anthropic':
            if not provider_config.api_key:
                raise ValueError("ANTHROPIC_API_KEY is required")
        
        elif self.provider == 'groq':
            if not provider_config.api_key:
                raise ValueError("GROQ_API_KEY is required")
        
        elif self.provider == 'gemini':
            if not provider_config.api_key:
                raise ValueError("GEMINI_API_KEY is required")
        
        return True
