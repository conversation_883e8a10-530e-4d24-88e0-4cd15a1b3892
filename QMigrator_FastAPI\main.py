"""
FastAPI application for QMigrator AI database migration system.

This module provides a RESTful API interface for the QMigrator AI database migration workflow,
offering endpoints for SQL conversion, validation, and deployment operations.
"""

import os
import sys
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.config_manager import ConfigManager
from llm.azure_openai_llm import create_llm


# Global variables for application state
config_manager = None
llm_instance = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for FastAPI.
    
    Handles application startup and shutdown events, including:
    - Configuration initialization
    - LLM provider setup
    - Resource cleanup
    """
    global config_manager, llm_instance
    
    # Startup
    try:
        print("Initializing QMigrator AI FastAPI application...")
        
        # Initialize configuration
        config_manager = ConfigManager()
        
        # Initialize LLM
        llm_provider = config_manager.get_llm_provider()
        print(f"Initializing {llm_provider} LLM...")
        llm_instance = create_llm(llm_provider, config_manager)
        
        print("Application initialized successfully!")
        
    except Exception as e:
        print(f"Failed to initialize application: {str(e)}")
        sys.exit(1)
    
    yield
    
    # Shutdown
    print("Shutting down QMigrator AI FastAPI application...")


# Create FastAPI application
app = FastAPI(
    title="QMigrator AI",
    description="Database Migration AI System - Convert SQL between different database systems",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint providing API information."""
    return {
        "message": "QMigrator AI - Database Migration System",
        "version": "1.0.0",
        "status": "active",
        "endpoints": {
            "conversion": "/conversion/*",
            "dba": "/dba/*",
            "health": "/health"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    global config_manager, llm_instance
    
    try:
        # Check if core components are initialized
        if config_manager is None:
            raise HTTPException(status_code=503, detail="Configuration not initialized")
        
        if llm_instance is None:
            raise HTTPException(status_code=503, detail="LLM not initialized")
        
        # Get configuration status
        source_db = config_manager.get_source_database()
        target_db = config_manager.get_target_database()
        provider = config_manager.get_llm_provider()
        
        return {
            "status": "healthy",
            "configuration": {
                "source_database": source_db,
                "target_database": target_db,
                "llm_provider": provider
            },
            "components": {
                "config_manager": "initialized",
                "llm_instance": "initialized"
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Health check failed: {str(e)}")


# Import and include routers
from routes import conversion, dba

app.include_router(conversion.router, prefix="/conversion", tags=["conversion"])
app.include_router(dba.router, prefix="/dba", tags=["database"])


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled errors."""
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": str(exc),
            "type": type(exc).__name__
        }
    )


if __name__ == "__main__":
    import uvicorn
    
    # Get port from environment variable or use default
    port = int(os.getenv("PORT", 8000))
    host = os.getenv("HOST", "0.0.0.0")
    
    print(f"Starting QMigrator AI FastAPI server on {host}:{port}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )
