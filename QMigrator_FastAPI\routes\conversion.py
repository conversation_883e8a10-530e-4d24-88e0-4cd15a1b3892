"""
Conversion routes for QMigrator AI FastAPI application.

This module provides a single comprehensive REST API endpoint for the complete
SQL conversion workflow from source to target database format.
"""

import uuid
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from state.state import ConversionRequest, ConversionResponse, WorkflowState


router = APIRouter()


@router.post("/process", response_model=ConversionResponse)
async def process_conversion_workflow(request: ConversionRequest) -> ConversionResponse:
    """
    Complete SQL conversion workflow endpoint.

    This single endpoint handles the entire database migration workflow including:
    - SQL statement splitting and parsing
    - Error identification and analysis
    - Source-to-target statement mapping
    - AI-powered statement conversion
    - Validation and correction
    - Deployment testing and error fixing
    - Iterative improvement until success or max iterations

    The workflow automatically handles:
    - Oracle to PostgreSQL conversion (or other database pairs)
    - Deployment error analysis and correction
    - Multiple iterations with AI learning
    - Comprehensive validation at each step

    Args:
        request: ConversionRequest containing:
            - source_code: Original source database SQL code
            - target_code: Target database SQL code (optional, can be generated)
            - deployment_error: Error message from deployment (optional)

    Returns:
        ConversionResponse containing:
            - success: Whether the workflow completed successfully
            - message: Status message with details
            - workflow_state: Complete workflow state with all results
            - iteration_count: Number of iterations performed
    """
    try:
        # Import here to avoid circular imports
        from main import config_manager, llm_instance

        if not config_manager or not llm_instance:
            raise HTTPException(
                status_code=503,
                detail="Application not properly initialized"
            )

        # Create initial workflow state
        workflow_state = WorkflowState(
            source_code=request.source_code,
            target_code=request.target_code or "",
            deployment_error=request.deployment_error
        )

        # Execute the complete conversion workflow
        result = await execute_complete_workflow(workflow_state, llm_instance, config_manager)

        # Determine success based on final state
        success = (
            result.deployment_successful == True or
            (result.deployment_successful is None and result.updated_target_code is not None)
        )

        # Create appropriate message
        if success:
            message = f"Conversion workflow completed successfully in {result.iteration_count} iterations"
        elif result.max_iterations_reached:
            message = f"Workflow reached maximum iterations ({result.iteration_count}). Manual review may be needed."
        else:
            message = "Conversion workflow completed with issues. Check workflow_state for details."

        return ConversionResponse(
            success=success,
            message=message,
            workflow_state=result,
            iteration_count=result.iteration_count
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Conversion workflow failed: {str(e)}"
        )


async def execute_complete_workflow(
    workflow_state: WorkflowState,
    llm_instance: Any,
    config_manager: Any
) -> WorkflowState:
    """
    Execute the complete conversion workflow.

    This function orchestrates the entire migration process using the same
    logic as the original QM_Conversion_Agent1 workflow.

    Args:
        workflow_state: Initial workflow state
        llm_instance: Initialized LLM instance
        config_manager: Configuration manager

    Returns:
        Updated workflow state with results
    """
    # Import workflow components
    from workflow.graph_builder import GraphBuilder

    # Create and setup the workflow graph
    graph_builder = GraphBuilder(llm_instance)
    graph_builder.setup_graph()

    # Prepare input data for the workflow
    input_data = {
        "source_code": workflow_state.source_code,
        "target_code": workflow_state.target_code,
        "deployment_error": workflow_state.deployment_error
    }

    # Generate unique thread ID for this workflow execution
    thread_id = str(uuid.uuid4())

    # Execute the workflow
    result = graph_builder.invoke_graph(input_data, thread_id)

    # Convert result back to WorkflowState
    # The graph_builder should return the final state
    if isinstance(result, dict):
        # Update the workflow state with results
        for key, value in result.items():
            if hasattr(workflow_state, key):
                setattr(workflow_state, key, value)

    return workflow_state



@router.get("/supported-databases")
async def get_supported_databases() -> Dict[str, Any]:
    """
    Get list of supported source and target databases.

    Returns:
        Dictionary containing supported database information
    """
    return {
        "source_databases": [
            "Oracle",
            "SQL Server",
            "MySQL",
            "PostgreSQL"
        ],
        "target_databases": [
            "PostgreSQL",
            "MySQL",
            "SQL Server",
            "Oracle"
        ],
        "popular_migrations": [
            "Oracle to PostgreSQL",
            "SQL Server to PostgreSQL",
            "MySQL to PostgreSQL"
        ]
    }
