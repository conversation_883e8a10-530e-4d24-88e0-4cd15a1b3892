"""
Conversion routes for QMigrator AI FastAPI application.

This module provides REST API endpoints for SQL conversion operations,
including statement conversion, validation, and workflow management.
"""

import uuid
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse

from state.state import ConversionRequest, ConversionResponse, WorkflowState
from workflow.conversion_workflow import ConversionWorkflow


router = APIRouter()


def get_conversion_workflow() -> ConversionWorkflow:
    """Dependency to get conversion workflow instance."""
    # This will be injected with the actual workflow instance
    # For now, we'll create a simple placeholder
    return ConversionWorkflow()


@router.post("/convert", response_model=ConversionResponse)
async def convert_sql(
    request: ConversionRequest,
    workflow: ConversionWorkflow = Depends(get_conversion_workflow)
) -> ConversionResponse:
    """
    Convert SQL code from source to target database format.
    
    This endpoint handles the complete SQL conversion workflow including:
    - Statement parsing and analysis
    - Error identification and correction
    - Validation and deployment testing
    
    Args:
        request: Conversion request containing source code and optional target code/error
        workflow: Conversion workflow instance
        
    Returns:
        ConversionResponse with workflow results and status
    """
    try:
        # Create initial workflow state
        workflow_state = WorkflowState(
            source_code=request.source_code,
            target_code=request.target_code or "",
            deployment_error=request.deployment_error
        )
        
        # Execute conversion workflow
        result = await workflow.execute_conversion(workflow_state)
        
        return ConversionResponse(
            success=True,
            message="Conversion completed successfully",
            workflow_state=result,
            iteration_count=result.iteration_count
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Conversion failed: {str(e)}"
        )


@router.post("/validate", response_model=ConversionResponse)
async def validate_conversion(
    request: ConversionRequest,
    workflow: ConversionWorkflow = Depends(get_conversion_workflow)
) -> ConversionResponse:
    """
    Validate SQL conversion results.
    
    This endpoint validates the conversion between source and target SQL code,
    checking for syntax correctness and semantic equivalence.
    
    Args:
        request: Validation request containing source and target code
        workflow: Conversion workflow instance
        
    Returns:
        ConversionResponse with validation results
    """
    try:
        if not request.target_code:
            raise HTTPException(
                status_code=400,
                detail="Target code is required for validation"
            )
        
        # Create workflow state for validation
        workflow_state = WorkflowState(
            source_code=request.source_code,
            target_code=request.target_code
        )
        
        # Execute validation
        result = await workflow.validate_conversion(workflow_state)
        
        return ConversionResponse(
            success=result.validation_successful or False,
            message="Validation completed",
            workflow_state=result,
            iteration_count=result.validation_attempts
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Validation failed: {str(e)}"
        )


@router.post("/fix-errors", response_model=ConversionResponse)
async def fix_deployment_errors(
    request: ConversionRequest,
    workflow: ConversionWorkflow = Depends(get_conversion_workflow)
) -> ConversionResponse:
    """
    Fix deployment errors in target SQL code.
    
    This endpoint analyzes deployment errors and attempts to fix them
    using AI-powered error correction and validation.
    
    Args:
        request: Error fixing request with target code and deployment error
        workflow: Conversion workflow instance
        
    Returns:
        ConversionResponse with error correction results
    """
    try:
        if not request.deployment_error:
            raise HTTPException(
                status_code=400,
                detail="Deployment error message is required"
            )
        
        if not request.target_code:
            raise HTTPException(
                status_code=400,
                detail="Target code is required for error fixing"
            )
        
        # Create workflow state for error fixing
        workflow_state = WorkflowState(
            source_code=request.source_code,
            target_code=request.target_code,
            deployment_error=request.deployment_error
        )
        
        # Execute error fixing workflow
        result = await workflow.fix_deployment_errors(workflow_state)
        
        return ConversionResponse(
            success=result.deployment_successful or False,
            message="Error fixing completed",
            workflow_state=result,
            iteration_count=result.iteration_count
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fixing failed: {str(e)}"
        )


@router.get("/status/{workflow_id}")
async def get_workflow_status(workflow_id: str) -> Dict[str, Any]:
    """
    Get the status of a running workflow.
    
    Args:
        workflow_id: Unique identifier for the workflow
        
    Returns:
        Dictionary containing workflow status information
    """
    try:
        # This would typically query a workflow tracking system
        # For now, return a placeholder response
        return {
            "workflow_id": workflow_id,
            "status": "completed",
            "progress": 100,
            "message": "Workflow completed successfully"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get workflow status: {str(e)}"
        )


@router.get("/supported-databases")
async def get_supported_databases() -> Dict[str, Any]:
    """
    Get list of supported source and target databases.
    
    Returns:
        Dictionary containing supported database information
    """
    return {
        "source_databases": [
            "Oracle",
            "SQL Server",
            "MySQL",
            "PostgreSQL"
        ],
        "target_databases": [
            "PostgreSQL",
            "MySQL",
            "SQL Server",
            "Oracle"
        ],
        "popular_migrations": [
            "Oracle to PostgreSQL",
            "SQL Server to PostgreSQL",
            "MySQL to PostgreSQL"
        ]
    }


@router.post("/split-statements")
async def split_sql_statements(request: Dict[str, str]) -> Dict[str, Any]:
    """
    Split SQL code into individual statements.
    
    Args:
        request: Dictionary containing 'sql_code' key
        
    Returns:
        Dictionary containing split statements
    """
    try:
        sql_code = request.get("sql_code", "")
        if not sql_code:
            raise HTTPException(
                status_code=400,
                detail="SQL code is required"
            )
        
        # This would use the actual SQL splitter from the workflow
        # For now, return a simple split by semicolon
        statements = [stmt.strip() for stmt in sql_code.split(';') if stmt.strip()]
        
        return {
            "statements": statements,
            "count": len(statements),
            "success": True
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to split statements: {str(e)}"
        )
