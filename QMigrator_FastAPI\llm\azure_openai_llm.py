"""
Azure OpenAI LLM integration for QMigrator AI FastAPI application.

This module provides Azure OpenAI integration for the database migration workflow.
"""

from typing import Any
from langchain_openai import AzureChatOpenAI
from config.config_manager import ConfigManager


def create_llm(provider: str, config_manager: ConfigManager) -> Any:
    """
    Create and initialize an LLM instance based on the provider.
    
    Args:
        provider: The LLM provider name
        config_manager: Configuration manager instance
        
    Returns:
        Initialized LLM instance
        
    Raises:
        ValueError: If the provider is not supported
        Exception: If LLM initialization fails
    """
    
    if provider == "azure_openai":
        return create_azure_openai_llm(config_manager)
    elif provider == "openai":
        return create_openai_llm(config_manager)
    elif provider == "anthropic":
        return create_anthropic_llm(config_manager)
    elif provider == "groq":
        return create_groq_llm(config_manager)
    elif provider == "gemini":
        return create_gemini_llm(config_manager)
    elif provider == "ollama":
        return create_ollama_llm(config_manager)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")


def create_azure_openai_llm(config_manager: ConfigManager) -> AzureChatOpenAI:
    """Create Azure OpenAI LLM instance."""
    azure_config = config_manager.models_config.azure_openai
    
    if not azure_config.api_key:
        raise ValueError("Azure OpenAI API key is required")
    if not azure_config.endpoint:
        raise ValueError("Azure OpenAI endpoint is required")
    if not azure_config.deployment_name:
        raise ValueError("Azure OpenAI deployment name is required")
    
    try:
        client = AzureChatOpenAI(
            azure_endpoint=azure_config.endpoint,
            azure_deployment=azure_config.deployment_name,
            api_key=azure_config.api_key,
            api_version=azure_config.api_version,
            temperature=azure_config.temperature,
            max_tokens=azure_config.max_tokens
        )
        print("Successfully initialized Azure OpenAI client")
        return client
    except Exception as e:
        raise Exception(f"Failed to initialize Azure OpenAI client: {str(e)}")


def create_openai_llm(config_manager: ConfigManager) -> Any:
    """Create OpenAI LLM instance."""
    from langchain_openai import ChatOpenAI
    
    openai_config = config_manager.models_config.openai
    
    if not openai_config.api_key:
        raise ValueError("OpenAI API key is required")
    
    try:
        client = ChatOpenAI(
            api_key=openai_config.api_key,
            model=openai_config.model_name,
            temperature=openai_config.temperature,
            max_tokens=openai_config.max_tokens
        )
        print("Successfully initialized OpenAI client")
        return client
    except Exception as e:
        raise Exception(f"Failed to initialize OpenAI client: {str(e)}")


def create_anthropic_llm(config_manager: ConfigManager) -> Any:
    """Create Anthropic LLM instance."""
    from langchain_anthropic import ChatAnthropic
    
    anthropic_config = config_manager.models_config.anthropic
    
    if not anthropic_config.api_key:
        raise ValueError("Anthropic API key is required")
    
    try:
        client = ChatAnthropic(
            api_key=anthropic_config.api_key,
            model=anthropic_config.model_name,
            temperature=anthropic_config.temperature,
            max_tokens=anthropic_config.max_tokens
        )
        print("Successfully initialized Anthropic client")
        return client
    except Exception as e:
        raise Exception(f"Failed to initialize Anthropic client: {str(e)}")


def create_groq_llm(config_manager: ConfigManager) -> Any:
    """Create Groq LLM instance."""
    from langchain_groq import ChatGroq
    
    groq_config = config_manager.models_config.groq
    
    if not groq_config.api_key:
        raise ValueError("Groq API key is required")
    
    try:
        client = ChatGroq(
            api_key=groq_config.api_key,
            model=groq_config.model_name,
            temperature=groq_config.temperature,
            max_tokens=groq_config.max_tokens
        )
        print("Successfully initialized Groq client")
        return client
    except Exception as e:
        raise Exception(f"Failed to initialize Groq client: {str(e)}")


def create_gemini_llm(config_manager: ConfigManager) -> Any:
    """Create Gemini LLM instance."""
    from langchain_google_genai import ChatGoogleGenerativeAI
    
    gemini_config = config_manager.models_config.gemini
    
    if not gemini_config.api_key:
        raise ValueError("Gemini API key is required")
    
    try:
        client = ChatGoogleGenerativeAI(
            google_api_key=gemini_config.api_key,
            model=gemini_config.model_name,
            temperature=gemini_config.temperature,
            max_output_tokens=gemini_config.max_tokens
        )
        print("Successfully initialized Gemini client")
        return client
    except Exception as e:
        raise Exception(f"Failed to initialize Gemini client: {str(e)}")


def create_ollama_llm(config_manager: ConfigManager) -> Any:
    """Create Ollama LLM instance."""
    from langchain_ollama import ChatOllama
    
    ollama_config = config_manager.models_config.ollama
    
    try:
        client = ChatOllama(
            base_url=ollama_config.base_url,
            model=ollama_config.model_name,
            temperature=ollama_config.temperature,
            num_predict=ollama_config.max_tokens
        )
        print("Successfully initialized Ollama client")
        return client
    except Exception as e:
        raise Exception(f"Failed to initialize Ollama client: {str(e)}")
